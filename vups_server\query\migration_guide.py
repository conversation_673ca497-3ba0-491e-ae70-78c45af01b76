"""
Migration guide and utilities for transitioning to the refactored query system.
This module provides tools and documentation for migrating from the old monolithic
query_vup_user_data.py to the new modular system.
"""

import asyncio
import time
from typing import Dict, List, Tuple

from vups.logger import logger
from vups_server.sql.create_comment_indexes import CommentIndexManager


class MigrationManager:
    """Manager for handling migration from old to new query system."""
    
    def __init__(self):
        self.comment_index_manager = CommentIndexManager()
    
    async def run_performance_comparison(self, uid: str) -> Dict:
        """
        Run performance comparison between old and new query methods.
        
        Args:
            uid: User UID to test with
            
        Returns:
            Dictionary containing performance comparison results
        """
        logger.info(f"Starting performance comparison for UID={uid}")
        
        # Import both old and new systems
        try:
            # Import old system (if available)
            from vups_server.query import query_vup_user_data as old_system
            old_available = True
        except ImportError:
            logger.warning("Old query system not available for comparison")
            old_available = False
        
        # Import new system
        from vups_server.query.query_vup_user_data_refactored import (
            query_current_stat_by_mid,
            query_user_dynamics_by_mid,
            query_all_video_list_by_mid,
            query_top_n_comments,
            query_top_n_videos,
        )
        
        results = {
            "uid": uid,
            "timestamp": time.time(),
            "tests": {},
            "summary": {}
        }
        
        # Test functions to compare
        test_functions = [
            ("query_current_stat_by_mid", query_current_stat_by_mid),
            ("query_user_dynamics_by_mid", query_user_dynamics_by_mid),
            ("query_all_video_list_by_mid", query_all_video_list_by_mid),
            ("query_top_n_comments", lambda u: query_top_n_comments(u, 10)),
            ("query_top_n_videos", lambda u: query_top_n_videos(u, 10)),
        ]
        
        for func_name, new_func in test_functions:
            logger.info(f"Testing {func_name}...")
            
            # Test new system
            start_time = time.time()
            try:
                new_result = await new_func(uid)
                new_duration = time.time() - start_time
                new_success = True
                new_result_size = len(new_result) if isinstance(new_result, (list, dict)) else 1
            except Exception as e:
                new_duration = time.time() - start_time
                new_success = False
                new_result_size = 0
                logger.error(f"New system error in {func_name}: {e}")
            
            # Test old system (if available)
            old_duration = None
            old_success = None
            old_result_size = 0
            
            if old_available:
                try:
                    old_func = getattr(old_system, func_name)
                    start_time = time.time()
                    if func_name in ["query_top_n_comments", "query_top_n_videos"]:
                        old_result = await old_func(uid, 10)
                    else:
                        old_result = await old_func(uid)
                    old_duration = time.time() - start_time
                    old_success = True
                    old_result_size = len(old_result) if isinstance(old_result, (list, dict)) else 1
                except Exception as e:
                    old_duration = time.time() - start_time
                    old_success = False
                    logger.error(f"Old system error in {func_name}: {e}")
            
            # Calculate improvement
            improvement = None
            if old_duration and new_duration:
                improvement = ((old_duration - new_duration) / old_duration) * 100
            
            results["tests"][func_name] = {
                "new_system": {
                    "duration": new_duration,
                    "success": new_success,
                    "result_size": new_result_size
                },
                "old_system": {
                    "duration": old_duration,
                    "success": old_success,
                    "result_size": old_result_size
                },
                "improvement_percent": improvement
            }
        
        # Calculate summary statistics
        successful_tests = [t for t in results["tests"].values() if t["new_system"]["success"]]
        if successful_tests:
            avg_new_duration = sum(t["new_system"]["duration"] for t in successful_tests) / len(successful_tests)
            results["summary"]["avg_new_duration"] = avg_new_duration
            
            if old_available:
                old_successful = [t for t in successful_tests if t["old_system"]["success"]]
                if old_successful:
                    avg_old_duration = sum(t["old_system"]["duration"] for t in old_successful) / len(old_successful)
                    avg_improvement = ((avg_old_duration - avg_new_duration) / avg_old_duration) * 100
                    results["summary"]["avg_old_duration"] = avg_old_duration
                    results["summary"]["avg_improvement_percent"] = avg_improvement
        
        logger.info(f"Performance comparison completed for UID={uid}")
        return results
    
    async def setup_database_optimizations(self) -> Dict:
        """
        Set up database optimizations including indexes.
        
        Returns:
            Dictionary containing setup results
        """
        logger.info("Starting database optimization setup...")
        
        results = {
            "timestamp": time.time(),
            "comment_indexes": {},
            "main_indexes": {"status": "pending"},
            "analysis": {"status": "pending"}
        }
        
        # Create comment table indexes
        try:
            comment_results = await self.comment_index_manager.create_all_comment_indexes()
            results["comment_indexes"] = comment_results
            logger.info("Comment table indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create comment indexes: {e}")
            results["comment_indexes"]["error"] = str(e)
        
        # Run main optimization SQL script
        try:
            from vups_server.sql.db_pool import get_connection
            
            # Read and execute optimization script
            optimization_script_path = "vups_server/sql/optimizations.sql"
            try:
                with open(optimization_script_path, 'r', encoding='utf-8') as f:
                    optimization_sql = f.read()
                
                # Split into individual statements and execute
                statements = [stmt.strip() for stmt in optimization_sql.split(';') if stmt.strip()]
                
                async with get_connection() as conn:
                    for stmt in statements:
                        if stmt.startswith('--') or not stmt:
                            continue
                        try:
                            await conn.execute(stmt)
                        except Exception as e:
                            logger.warning(f"Failed to execute statement: {stmt[:50]}... Error: {e}")
                
                results["main_indexes"]["status"] = "completed"
                logger.info("Main database indexes created successfully")
                
            except FileNotFoundError:
                logger.warning("Optimization SQL script not found")
                results["main_indexes"]["status"] = "script_not_found"
            
        except Exception as e:
            logger.error(f"Failed to create main indexes: {e}")
            results["main_indexes"]["error"] = str(e)
            results["main_indexes"]["status"] = "failed"
        
        # Analyze tables
        try:
            analysis_results = await self.comment_index_manager.analyze_comment_tables()
            results["analysis"] = analysis_results
            logger.info("Table analysis completed successfully")
        except Exception as e:
            logger.error(f"Failed to analyze tables: {e}")
            results["analysis"]["error"] = str(e)
        
        logger.info("Database optimization setup completed")
        return results
    
    def generate_migration_report(self, performance_results: Dict, optimization_results: Dict) -> str:
        """
        Generate a comprehensive migration report.
        
        Args:
            performance_results: Results from performance comparison
            optimization_results: Results from database optimization
            
        Returns:
            Formatted migration report string
        """
        report = []
        report.append("=" * 80)
        report.append("VUPS QUERY SYSTEM MIGRATION REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Performance Summary
        report.append("PERFORMANCE COMPARISON SUMMARY")
        report.append("-" * 40)
        
        if "summary" in performance_results:
            summary = performance_results["summary"]
            if "avg_improvement_percent" in summary:
                report.append(f"Average Performance Improvement: {summary['avg_improvement_percent']:.1f}%")
                report.append(f"Average New System Duration: {summary['avg_new_duration']:.3f}s")
                report.append(f"Average Old System Duration: {summary['avg_old_duration']:.3f}s")
            else:
                report.append("Performance comparison with old system not available")
                report.append(f"Average New System Duration: {summary.get('avg_new_duration', 'N/A'):.3f}s")
        
        report.append("")
        
        # Detailed Test Results
        report.append("DETAILED TEST RESULTS")
        report.append("-" * 40)
        
        for func_name, test_result in performance_results.get("tests", {}).items():
            new_sys = test_result["new_system"]
            old_sys = test_result["old_system"]
            improvement = test_result.get("improvement_percent")
            
            report.append(f"Function: {func_name}")
            report.append(f"  New System: {new_sys['duration']:.3f}s ({'✓' if new_sys['success'] else '✗'})")
            if old_sys["duration"]:
                report.append(f"  Old System: {old_sys['duration']:.3f}s ({'✓' if old_sys['success'] else '✗'})")
                if improvement:
                    report.append(f"  Improvement: {improvement:.1f}%")
            report.append("")
        
        # Database Optimization Results
        report.append("DATABASE OPTIMIZATION RESULTS")
        report.append("-" * 40)
        
        comment_indexes = optimization_results.get("comment_indexes", {})
        report.append(f"Comment Table Indexes:")
        report.append(f"  Total Processed: {comment_indexes.get('total_processed', 0)}")
        report.append(f"  Successful: {comment_indexes.get('successful', 0)}")
        report.append(f"  Failed: {comment_indexes.get('failed', 0)}")
        
        main_indexes = optimization_results.get("main_indexes", {})
        report.append(f"Main Indexes: {main_indexes.get('status', 'unknown')}")
        
        analysis = optimization_results.get("analysis", {})
        if isinstance(analysis, dict) and "total_tables" in analysis:
            report.append(f"Table Analysis:")
            report.append(f"  Total Tables: {analysis.get('total_tables', 0)}")
            report.append(f"  Successful: {analysis.get('successful', 0)}")
            report.append(f"  Failed: {analysis.get('failed', 0)}")
        
        report.append("")
        
        # Recommendations
        report.append("RECOMMENDATIONS")
        report.append("-" * 40)
        
        if performance_results.get("summary", {}).get("avg_improvement_percent", 0) > 0:
            report.append("✓ Performance improvements detected - migration recommended")
        else:
            report.append("⚠ No significant performance improvement detected")
        
        if comment_indexes.get("successful", 0) > 0:
            report.append("✓ Comment table indexes created successfully")
        else:
            report.append("⚠ Comment table index creation had issues")
        
        if main_indexes.get("status") == "completed":
            report.append("✓ Main database indexes created successfully")
        else:
            report.append("⚠ Main database index creation had issues")
        
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)


async def run_full_migration(uid: str) -> str:
    """
    Run complete migration process including performance testing and optimization.
    
    Args:
        uid: User UID to test with
        
    Returns:
        Migration report string
    """
    manager = MigrationManager()
    
    logger.info("Starting full migration process...")
    
    # Run performance comparison
    performance_results = await manager.run_performance_comparison(uid)
    
    # Set up database optimizations
    optimization_results = await manager.setup_database_optimizations()
    
    # Generate report
    report = manager.generate_migration_report(performance_results, optimization_results)
    
    logger.info("Full migration process completed")
    return report


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python migration_guide.py <uid>")
        sys.exit(1)
    
    uid = sys.argv[1]
    report = asyncio.run(run_full_migration(uid))
    print(report)
