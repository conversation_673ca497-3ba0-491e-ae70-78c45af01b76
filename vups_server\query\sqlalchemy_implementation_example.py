"""
Practical SQLAlchemy implementation example for VUPS query system.
This demonstrates how to implement the hybrid approach with concrete examples.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional
import time

from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, DateTime, BigInteger, Text, select, and_, desc, func, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
import asyncpg

from vups.logger import logger
from vups_server.query.base import CacheManager


class SQLAlchemyUserStatsService:
    """
    Example implementation of user statistics service using SQLAlchemy Core.
    This shows how Phase 1 migration would work in practice.
    """
    
    def __init__(self, database_url: str):
        self.engine = create_async_engine(database_url, echo=False)
        self.async_session = sessionmaker(self.engine, class_=AsyncSession, expire_on_commit=False)
        self.cache = CacheManager(default_ttl=300)
        
        # Define table schema
        self.metadata = MetaData()
        self.current_stat_table = Table(
            'current_stat_table', self.metadata,
            Column('uid', String),
            Column('name', String),
            Column('datetime', DateTime),
            Column('video_total_num', Integer),
            Column('article_total_num', Integer),
            Column('likes_total_num', BigInteger),
            Column('elec_num', Integer),
            Column('follower_num', Integer),
            Column('dahanghai_num', Integer),
        )
    
    async def get_current_stat_by_uid(self, uid: str) -> Optional[Dict]:
        """
        Get current statistics using SQLAlchemy Core.
        Direct replacement for the raw SQL version.
        """
        cache_key = f"current_stat_{uid}"
        
        # Check cache first
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        async with self.async_session() as session:
            # Build query using SQLAlchemy Core
            query = select(self.current_stat_table).where(
                self.current_stat_table.c.uid == uid
            ).order_by(desc(self.current_stat_table.c.datetime)).limit(1)
            
            result = await session.execute(query)
            row = result.fetchone()
            
            if row:
                stat_data = {
                    "uid": row.uid,
                    "name": row.name,
                    "datetime": row.datetime,
                    "video_total_num": row.video_total_num,
                    "article_total_num": row.article_total_num,
                    "likes_total_num": row.likes_total_num,
                    "elec_num": row.elec_num,
                    "follower_num": row.follower_num,
                    "dahanghai_num": row.dahanghai_num,
                }
                
                # Cache the result
                await self.cache.set(cache_key, stat_data)
                return stat_data
            
            return None
    
    async def get_follower_history_range(self, uid: str, start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        Get follower history using SQLAlchemy Core with date range.
        Shows how to handle date operations.
        """
        cache_key = f"follower_history_{uid}_{start_date.date()}_{end_date.date()}"
        
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        async with self.async_session() as session:
            # SQLAlchemy Core query with date filtering
            query = select(
                self.current_stat_table.c.datetime,
                self.current_stat_table.c.follower_num,
                self.current_stat_table.c.name
            ).where(
                and_(
                    self.current_stat_table.c.uid == uid,
                    self.current_stat_table.c.datetime >= start_date,
                    self.current_stat_table.c.datetime <= end_date
                )
            ).order_by(self.current_stat_table.c.datetime)
            
            result = await session.execute(query)
            rows = result.fetchall()
            
            history_data = [
                {
                    "datetime": row.datetime.isoformat() if row.datetime else None,
                    "follower_num": row.follower_num,
                    "name": row.name,
                }
                for row in rows
            ]
            
            await self.cache.set(cache_key, history_data)
            return history_data
    
    async def calculate_growth_rate_sqlalchemy(self, uid: str, days: int = 30) -> Optional[float]:
        """
        Calculate growth rate using SQLAlchemy with window functions.
        Shows how to handle complex calculations.
        """
        async with self.async_session() as session:
            # Use SQLAlchemy's func for window functions
            query = select(
                self.current_stat_table.c.follower_num,
                func.lag(self.current_stat_table.c.follower_num).over(
                    order_by=self.current_stat_table.c.datetime
                ).label('prev_followers')
            ).where(
                and_(
                    self.current_stat_table.c.uid == uid,
                    self.current_stat_table.c.datetime >= func.now() - text(f"INTERVAL '{days} days'")
                )
            ).order_by(self.current_stat_table.c.datetime)
            
            result = await session.execute(query)
            rows = result.fetchall()
            
            if len(rows) < 2:
                return None
            
            first_followers = rows[0].follower_num
            last_followers = rows[-1].follower_num
            
            if first_followers and first_followers > 0:
                growth_rate = ((last_followers - first_followers) / first_followers) * 100
                return round(growth_rate, 2)
            
            return None


class HybridCommentService:
    """
    Example of hybrid approach for dynamic comment tables.
    Uses SQLAlchemy text() for dynamic table names.
    """
    
    def __init__(self, database_url: str):
        self.engine = create_async_engine(database_url)
        self.async_session = sessionmaker(self.engine, class_=AsyncSession, expire_on_commit=False)
        self.cache = CacheManager(default_ttl=600)
    
    async def get_video_comments_hybrid(self, uid: str, limit: int = 100) -> List[Dict]:
        """
        Get video comments using hybrid SQLAlchemy + dynamic table approach.
        """
        cache_key = f"video_comments_hybrid_{uid}_{limit}"
        
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        async with self.async_session() as session:
            comment_table_name = f"video_comment_{uid}"
            
            # Check if table exists using SQLAlchemy
            table_exists_query = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = :table_name
                )
            """)
            
            result = await session.execute(table_exists_query, {"table_name": comment_table_name})
            table_exists = result.scalar()
            
            if not table_exists:
                logger.warning(f"Comment table {comment_table_name} does not exist")
                return []
            
            # Query the dynamic table using text()
            comments_query = text(f"""
                SELECT 
                    comment_id,
                    bvid,
                    comment_content,
                    comment_time,
                    like_num,
                    comment_user_uid,
                    comment_user_name
                FROM {comment_table_name}
                WHERE comment_content IS NOT NULL
                ORDER BY comment_time DESC
                LIMIT :limit
            """)
            
            result = await session.execute(comments_query, {"limit": limit})
            rows = result.fetchall()
            
            comments_data = [
                {
                    "comment_id": row.comment_id,
                    "bvid": row.bvid,
                    "comment_content": row.comment_content,
                    "comment_time": row.comment_time.isoformat() if row.comment_time else None,
                    "like_num": row.like_num,
                    "comment_user_uid": row.comment_user_uid,
                    "comment_user_name": row.comment_user_name,
                }
                for row in rows
            ]
            
            await self.cache.set(cache_key, comments_data)
            return comments_data
    
    async def get_top_comments_by_likes_hybrid(self, uid: str, limit: int = 10) -> List[Dict]:
        """
        Get top comments by likes using hybrid approach.
        Shows how to handle ORDER BY and aggregations.
        """
        cache_key = f"top_comments_hybrid_{uid}_{limit}"
        
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        async with self.async_session() as session:
            comment_table_name = f"video_comment_{uid}"
            
            # Check table existence
            table_exists_query = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = :table_name
                )
            """)
            
            result = await session.execute(table_exists_query, {"table_name": comment_table_name})
            if not result.scalar():
                return []
            
            # Get top comments with SQLAlchemy text()
            top_comments_query = text(f"""
                SELECT 
                    comment_id,
                    bvid,
                    comment_content,
                    comment_time,
                    like_num,
                    comment_user_uid,
                    comment_user_name
                FROM {comment_table_name}
                WHERE like_num > 0 
                    AND comment_content IS NOT NULL
                    AND LENGTH(comment_content) > 10
                ORDER BY like_num DESC, comment_time DESC
                LIMIT :limit
            """)
            
            result = await session.execute(top_comments_query, {"limit": limit})
            rows = result.fetchall()
            
            top_comments = [
                {
                    "comment_id": row.comment_id,
                    "bvid": row.bvid,
                    "comment_content": row.comment_content,
                    "comment_time": row.comment_time.isoformat() if row.comment_time else None,
                    "like_num": row.like_num,
                    "comment_user_uid": row.comment_user_uid,
                    "comment_user_name": row.comment_user_name,
                }
                for row in rows
            ]
            
            await self.cache.set(cache_key, top_comments)
            return top_comments


class PerformanceComparison:
    """
    Tool for comparing performance between different approaches.
    """
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.sqlalchemy_service = SQLAlchemyUserStatsService(database_url)
        self.asyncpg_pool = None
    
    async def init_asyncpg(self):
        """Initialize asyncpg pool for comparison."""
        self.asyncpg_pool = await asyncpg.create_pool(self.database_url)
    
    async def compare_current_stat_query(self, uid: str, iterations: int = 10) -> Dict:
        """
        Compare performance between SQLAlchemy and raw SQL for current stat query.
        """
        if not self.asyncpg_pool:
            await self.init_asyncpg()
        
        # Test SQLAlchemy approach
        sqlalchemy_times = []
        for _ in range(iterations):
            start_time = time.time()
            result = await self.sqlalchemy_service.get_current_stat_by_uid(uid)
            sqlalchemy_times.append(time.time() - start_time)
        
        # Test raw SQL approach
        raw_sql_times = []
        for _ in range(iterations):
            start_time = time.time()
            async with self.asyncpg_pool.acquire() as conn:
                result = await conn.fetchrow(
                    "SELECT * FROM current_stat_table WHERE uid = $1 ORDER BY datetime DESC LIMIT 1",
                    uid
                )
            raw_sql_times.append(time.time() - start_time)
        
        avg_sqlalchemy = sum(sqlalchemy_times) / len(sqlalchemy_times)
        avg_raw_sql = sum(raw_sql_times) / len(raw_sql_times)
        
        return {
            "sqlalchemy_avg_time": avg_sqlalchemy,
            "raw_sql_avg_time": avg_raw_sql,
            "performance_difference_percent": ((avg_sqlalchemy - avg_raw_sql) / avg_raw_sql) * 100,
            "sqlalchemy_times": sqlalchemy_times,
            "raw_sql_times": raw_sql_times,
            "iterations": iterations,
        }
    
    async def cleanup(self):
        """Clean up connections."""
        if self.asyncpg_pool:
            await self.asyncpg_pool.close()
        await self.sqlalchemy_service.engine.dispose()


# Example usage and testing
async def run_migration_example():
    """
    Example of how to run the migration and performance testing.
    """
    database_url = "postgresql+asyncpg://user:password@localhost/vups_db"
    
    # Initialize services
    sqlalchemy_service = SQLAlchemyUserStatsService(database_url)
    hybrid_service = HybridCommentService(database_url)
    performance_tool = PerformanceComparison(database_url)
    
    test_uid = "123456"
    
    try:
        # Test SQLAlchemy Core approach
        print("Testing SQLAlchemy Core approach...")
        current_stat = await sqlalchemy_service.get_current_stat_by_uid(test_uid)
        print(f"Current stat result: {current_stat}")
        
        # Test hybrid approach for comments
        print("Testing hybrid approach for comments...")
        comments = await hybrid_service.get_video_comments_hybrid(test_uid, 10)
        print(f"Found {len(comments)} comments")
        
        # Performance comparison
        print("Running performance comparison...")
        comparison = await performance_tool.compare_current_stat_query(test_uid, 5)
        print(f"SQLAlchemy avg: {comparison['sqlalchemy_avg_time']:.4f}s")
        print(f"Raw SQL avg: {comparison['raw_sql_avg_time']:.4f}s")
        print(f"Performance difference: {comparison['performance_difference_percent']:.1f}%")
        
    finally:
        await performance_tool.cleanup()


if __name__ == "__main__":
    asyncio.run(run_migration_example())
