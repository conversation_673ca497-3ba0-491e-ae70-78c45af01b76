"""
Comprehensive mock tests for vups_server.query.query_vup_user_data_refactored module.
Tests all functions with proper mocking of external dependencies.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Optional

# Import the module under test
import vups_server.query.query_vup_user_data_refactored as query_module


class TestUserStatisticsFunctions:
    """Tests for functions that use user_stats_service."""

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_stat_by_mid_success(self, mock_service):
        """Test successful current stat query."""
        # Setup
        mock_record = MagicMock()
        mock_record.__getitem__ = MagicMock(side_effect=lambda key: f"value_{key}")
        mock_service.get_current_stat_by_uid.return_value = mock_record

        # Execute
        result = await query_module.query_current_stat_by_mid("401315430")

        # Assert
        assert result == mock_record
        mock_service.get_current_stat_by_uid.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_stat_by_mid_none_result(self, mock_service):
        """Test current stat query returning None."""
        # Setup
        mock_service.get_current_stat_by_uid.return_value = None

        # Execute
        result = await query_module.query_current_stat_by_mid("401315430")

        # Assert
        assert result is None
        mock_service.get_current_stat_by_uid.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_peroid_user_all_stat_by_uid_and_time(self, mock_service):
        """Test period user stats query."""
        # Setup
        mock_stats = [["2025-01-01", 100, 200], ["2025-05-01", 150, 250]]
        mock_service.get_user_stats_by_time_range.return_value = mock_stats

        # Execute
        result = await query_module.query_peroid_user_all_stat_by_uid_and_time(
            "401315430", "2025-01-01", "2025-05-01"
        )

        # Assert
        assert result == mock_stats
        mock_service.get_user_stats_by_time_range.assert_called_once_with(
            "401315430", "2025-01-01", "2025-05-01"
        )

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_whole_user_all_stat_by_uid_and_recent_default(self, mock_service):
        """Test recent user stats query with default days."""
        # Setup
        mock_stats = [["2025-01-01", 100, 200]]
        mock_service.get_user_stats_recent_days.return_value = mock_stats

        # Execute
        result = await query_module.query_whole_user_all_stat_by_uid_and_recent("401315430")

        # Assert
        assert result == mock_stats
        mock_service.get_user_stats_recent_days.assert_called_once_with("401315430", -1)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_whole_user_all_stat_by_uid_and_recent_custom_days(self, mock_service):
        """Test recent user stats query with custom days."""
        # Setup
        mock_stats = [["2025-01-01", 100, 200]]
        mock_service.get_user_stats_recent_days.return_value = mock_stats

        # Execute
        result = await query_module.query_whole_user_all_stat_by_uid_and_recent("401315430", 30)

        # Assert
        assert result == mock_stats
        mock_service.get_user_stats_recent_days.assert_called_once_with("401315430", 30)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_now_user_follower_num_by_mid(self, mock_service):
        """Test current follower count query."""
        # Setup
        mock_service.get_current_follower_count.return_value = 1000

        # Execute
        result = await query_module.query_now_user_follower_num_by_mid("401315430")

        # Assert
        assert result == 1000
        mock_service.get_current_follower_count.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_now_user_follower_num_by_mid_error(self, mock_service):
        """Test current follower count query returning -1 on error."""
        # Setup
        mock_service.get_current_follower_count.return_value = -1

        # Execute
        result = await query_module.query_now_user_follower_num_by_mid("401315430")

        # Assert
        assert result == -1
        mock_service.get_current_follower_count.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_calculate_follower_rate_by_mid_default_days(self, mock_service):
        """Test follower growth rate calculation with default days."""
        # Setup
        mock_service.calculate_follower_growth_rate.return_value = "+5.2%"

        # Execute
        result = await query_module.calculate_follower_rate_by_mid("401315430")

        # Assert
        assert result == "+5.2%"
        mock_service.calculate_follower_growth_rate.assert_called_once_with("401315430", 90)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_calculate_follower_rate_by_mid_custom_days(self, mock_service):
        """Test follower growth rate calculation with custom days."""
        # Setup
        mock_service.calculate_follower_growth_rate.return_value = "+3.1%"

        # Execute
        result = await query_module.calculate_follower_rate_by_mid("401315430", 30)

        # Assert
        assert result == "+3.1%"
        mock_service.calculate_follower_growth_rate.assert_called_once_with("401315430", 30)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_follower_change_num_default(self, mock_service):
        """Test follower change calculation with default days."""
        # Setup
        mock_service.get_follower_change.return_value = 50

        # Execute
        result = await query_module.query_current_follower_change_num("401315430")

        # Assert
        assert result == 50
        mock_service.get_follower_change.assert_called_once_with("401315430", 1)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_follower_change_num_custom_days(self, mock_service):
        """Test follower change calculation with custom days."""
        # Setup
        mock_service.get_follower_change.return_value = 200

        # Execute
        result = await query_module.query_current_follower_change_num("401315430", 7)

        # Assert
        assert result == 200
        mock_service.get_follower_change.assert_called_once_with("401315430", 7)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_follower_change_num_none_result(self, mock_service):
        """Test follower change calculation returning None."""
        # Setup
        mock_service.get_follower_change.return_value = None

        # Execute
        result = await query_module.query_current_follower_change_num("401315430")

        # Assert
        assert result is None
        mock_service.get_follower_change.assert_called_once_with("401315430", 1)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_whole_user_follower_num_by_mid_and_recent_default(self, mock_service):
        """Test follower history query with default days."""
        # Setup
        mock_history = [["2025-01-01", 1000], ["2025-05-01", 1050]]
        mock_service.get_follower_history.return_value = mock_history

        # Execute
        result = await query_module.query_whole_user_follower_num_by_mid_and_recent("401315430")

        # Assert
        assert result == mock_history
        mock_service.get_follower_history.assert_called_once_with("401315430", -1)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_whole_user_follower_num_by_mid_and_recent_custom_days(self, mock_service):
        """Test follower history query with custom days."""
        # Setup
        mock_history = [["2025-01-01", 1000], ["2025-05-01", 1050]]
        mock_service.get_follower_history.return_value = mock_history

        # Execute
        result = await query_module.query_whole_user_follower_num_by_mid_and_recent("401315430", 30)

        # Assert
        assert result == mock_history
        mock_service.get_follower_history.assert_called_once_with("401315430", 30)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_whole_dahanghai_num_by_mid_and_recent_default(self, mock_service):
        """Test dahanghai history query with default days."""
        # Setup
        mock_history = [["2025-01-01", 50], ["2025-05-01", 55]]
        mock_service.get_dahanghai_history.return_value = mock_history

        # Execute
        result = await query_module.query_whole_dahanghai_num_by_mid_and_recent("401315430")

        # Assert
        assert result == mock_history
        mock_service.get_dahanghai_history.assert_called_once_with("401315430", -1)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_whole_dahanghai_num_by_mid_and_recent_custom_days(self, mock_service):
        """Test dahanghai history query with custom days."""
        # Setup
        mock_history = [["2025-01-01", 50], ["2025-05-01", 55]]
        mock_service.get_dahanghai_history.return_value = mock_history

        # Execute
        result = await query_module.query_whole_dahanghai_num_by_mid_and_recent("401315430", 7)

        # Assert
        assert result == mock_history
        mock_service.get_dahanghai_history.assert_called_once_with("401315430", 7)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_now_user_dahanghai_num_by_mid(self, mock_service):
        """Test current dahanghai count query."""
        # Setup
        mock_service.get_current_dahanghai_count.return_value = 25

        # Execute
        result = await query_module.query_now_user_dahanghai_num_by_mid("401315430")

        # Assert
        assert result == 25
        mock_service.get_current_dahanghai_count.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_now_user_dahanghai_num_by_mid_error(self, mock_service):
        """Test current dahanghai count query returning -1 on error."""
        # Setup
        mock_service.get_current_dahanghai_count.return_value = -1

        # Execute
        result = await query_module.query_now_user_dahanghai_num_by_mid("401315430")

        # Assert
        assert result == -1
        mock_service.get_current_dahanghai_count.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_calculate_dahanghai_rate_by_mid_default_days(self, mock_service):
        """Test dahanghai growth rate calculation with default days."""
        # Setup
        mock_service.calculate_dahanghai_growth_rate.return_value = "+8.5%"

        # Execute
        result = await query_module.calculate_dahanghai_rate_by_mid("401315430")

        # Assert
        assert result == "+8.5%"
        mock_service.calculate_dahanghai_growth_rate.assert_called_once_with("401315430", 90)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_calculate_dahanghai_rate_by_mid_custom_days(self, mock_service):
        """Test dahanghai growth rate calculation with custom days."""
        # Setup
        mock_service.calculate_dahanghai_growth_rate.return_value = "+12.3%"

        # Execute
        result = await query_module.calculate_dahanghai_rate_by_mid("401315430", 30)

        # Assert
        assert result == "+12.3%"
        mock_service.calculate_dahanghai_growth_rate.assert_called_once_with("401315430", 30)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_dahanghai_change_num_default(self, mock_service):
        """Test dahanghai change calculation with default days."""
        # Setup
        mock_service.get_dahanghai_change.return_value = 3

        # Execute
        result = await query_module.query_current_dahanghai_change_num("401315430")

        # Assert
        assert result == 3
        mock_service.get_dahanghai_change.assert_called_once_with("401315430", 1)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_dahanghai_change_num_custom_days(self, mock_service):
        """Test dahanghai change calculation with custom days."""
        # Setup
        mock_service.get_dahanghai_change.return_value = 10

        # Execute
        result = await query_module.query_current_dahanghai_change_num("401315430", 7)

        # Assert
        assert result == 10
        mock_service.get_dahanghai_change.assert_called_once_with("401315430", 7)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_query_current_dahanghai_change_num_none_result(self, mock_service):
        """Test dahanghai change calculation returning None."""
        # Setup
        mock_service.get_dahanghai_change.return_value = None

        # Execute
        result = await query_module.query_current_dahanghai_change_num("401315430")

        # Assert
        assert result is None
        mock_service.get_dahanghai_change.assert_called_once_with("401315430", 1)


class TestContentQueryFunctions:
    """Tests for functions that use content_service."""

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_user_info_by_mid_success(self, mock_service):
        """Test successful user info query."""
        # Setup
        mock_record = MagicMock()
        mock_record.__getitem__ = MagicMock(side_effect=lambda key: f"value_{key}")
        mock_service.get_user_info_by_uid.return_value = mock_record

        # Execute
        result = await query_module.query_user_info_by_mid("401315430")

        # Assert
        assert result == mock_record
        mock_service.get_user_info_by_uid.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_user_info_by_mid_none_result(self, mock_service):
        """Test user info query returning None."""
        # Setup
        mock_service.get_user_info_by_uid.return_value = None

        # Execute
        result = await query_module.query_user_info_by_mid("401315430")

        # Assert
        assert result is None
        mock_service.get_user_info_by_uid.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_user_dynamics_by_mid(self, mock_service):
        """Test user dynamics query."""
        # Setup
        mock_dynamics = [
            {"dynamic_id": "123", "content": "test", "heat": 100},
            {"dynamic_id": "456", "content": "test2", "heat": 200}
        ]
        mock_service.get_user_dynamics.return_value = mock_dynamics

        # Execute
        result = await query_module.query_user_dynamics_by_mid("401315430")

        # Assert
        assert result == mock_dynamics
        mock_service.get_user_dynamics.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_all_video_list_by_mid(self, mock_service):
        """Test user videos query."""
        # Setup
        mock_videos = [
            {"bvid": "BV123", "title": "test video", "heat": 150},
            {"bvid": "BV456", "title": "test video 2", "heat": 300}
        ]
        mock_service.get_user_videos.return_value = mock_videos

        # Execute
        result = await query_module.query_all_video_list_by_mid("401315430")

        # Assert
        assert result == mock_videos
        mock_service.get_user_videos.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_current_videos(self, mock_service):
        """Test latest video query."""
        # Setup
        mock_video = [{"bvid": "BV123", "title": "latest video"}]
        mock_service.get_latest_video.return_value = mock_video

        # Execute
        result = await query_module.query_current_videos("401315430")

        # Assert
        assert result == mock_video
        mock_service.get_latest_video.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_current_dynamics(self, mock_service):
        """Test latest dynamic query."""
        # Setup
        mock_dynamic = [{"dynamic_id": "123", "content": "latest dynamic"}]
        mock_service.get_latest_dynamic.return_value = mock_dynamic

        # Execute
        result = await query_module.query_current_dynamics("401315430")

        # Assert
        assert result == mock_dynamic
        mock_service.get_latest_dynamic.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_recent_top_n_videos_default_limit(self, mock_service):
        """Test recent top videos query with default limit."""
        # Setup
        mock_videos = [{"bvid": "BV123", "title": "top video", "heat": 500}]
        mock_service.get_recent_top_videos.return_value = mock_videos

        # Execute
        result = await query_module.query_recent_top_n_videos("401315430")

        # Assert
        assert result == mock_videos
        mock_service.get_recent_top_videos.assert_called_once_with("401315430", 10)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_recent_top_n_videos_custom_limit(self, mock_service):
        """Test recent top videos query with custom limit."""
        # Setup
        mock_videos = [{"bvid": "BV123", "title": "top video", "heat": 500}]
        mock_service.get_recent_top_videos.return_value = mock_videos

        # Execute
        result = await query_module.query_recent_top_n_videos("401315430", 5)

        # Assert
        assert result == mock_videos
        mock_service.get_recent_top_videos.assert_called_once_with("401315430", 5)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_comments_for_wordcloud_default_limit(self, mock_service):
        """Test comments for wordcloud query with default limit."""
        # Setup
        mock_comments = ["comment 1", "comment 2", "comment 3"]
        mock_service.get_comments_for_wordcloud.return_value = mock_comments

        # Execute
        result = await query_module.query_comments_for_wordcloud("401315430")

        # Assert
        assert result == mock_comments
        mock_service.get_comments_for_wordcloud.assert_called_once_with("401315430", 1000)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_comments_for_wordcloud_custom_limit(self, mock_service):
        """Test comments for wordcloud query with custom limit."""
        # Setup
        mock_comments = ["comment 1", "comment 2"]
        mock_service.get_comments_for_wordcloud.return_value = mock_comments

        # Execute
        result = await query_module.query_comments_for_wordcloud("401315430", 500)

        # Assert
        assert result == mock_comments
        mock_service.get_comments_for_wordcloud.assert_called_once_with("401315430", 500)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_all_video_comments_by_mid(self, mock_service):
        """Test video comments query."""
        # Setup
        mock_comments = [
            {"comment_id": "123", "content": "great video", "like_num": 10},
            {"comment_id": "456", "content": "nice work", "like_num": 5}
        ]
        mock_service.get_video_comments.return_value = mock_comments

        # Execute
        result = await query_module.query_all_video_comments_by_mid("401315430")

        # Assert
        assert result == mock_comments
        mock_service.get_video_comments.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_query_all_dynamics_comments_by_mid(self, mock_service):
        """Test dynamics comments query."""
        # Setup
        mock_comments = [
            {"comment_id": "789", "content": "interesting post", "like_num": 8},
            {"comment_id": "101", "content": "thanks for sharing", "like_num": 3}
        ]
        mock_service.get_dynamics_comments.return_value = mock_comments

        # Execute
        result = await query_module.query_all_dynamics_comments_by_mid("401315430")

        # Assert
        assert result == mock_comments
        mock_service.get_dynamics_comments.assert_called_once_with("401315430")


class TestAnalyticsFunctions:
    """Tests for functions that use analytics_service."""

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_top_n_comments_default_limit(self, mock_service):
        """Test top comments query with default limit."""
        # Setup
        mock_comments = [
            {"comment_id": "123", "content": "top comment", "like_num": 100},
            {"comment_id": "456", "content": "second comment", "like_num": 80}
        ]
        mock_service.get_top_comments.return_value = mock_comments

        # Execute
        result = await query_module.query_top_n_comments("401315430")

        # Assert
        assert result == mock_comments
        mock_service.get_top_comments.assert_called_once_with("401315430", 10)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_top_n_comments_custom_limit(self, mock_service):
        """Test top comments query with custom limit."""
        # Setup
        mock_comments = [{"comment_id": "123", "content": "top comment", "like_num": 100}]
        mock_service.get_top_comments.return_value = mock_comments

        # Execute
        result = await query_module.query_top_n_comments("401315430", 5)

        # Assert
        assert result == mock_comments
        mock_service.get_top_comments.assert_called_once_with("401315430", 5)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_top_n_comments_user_default_limit(self, mock_service):
        """Test top comment users query with default limit."""
        # Setup
        mock_users = [
            {"user_uid": "111", "user_name": "top_commenter", "comment_count": 50},
            {"user_uid": "222", "user_name": "active_user", "comment_count": 30}
        ]
        mock_service.get_top_comment_users.return_value = mock_users

        # Execute
        result = await query_module.query_top_n_comments_user("401315430")

        # Assert
        assert result == mock_users
        mock_service.get_top_comment_users.assert_called_once_with("401315430", 10)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_top_n_videos_default_limit(self, mock_service):
        """Test top videos query with default limit."""
        # Setup
        mock_videos = [
            {"bvid": "BV123", "title": "top video", "heat": 1000},
            {"bvid": "BV456", "title": "popular video", "heat": 800}
        ]
        mock_service.get_top_videos.return_value = mock_videos

        # Execute
        result = await query_module.query_top_n_videos("401315430")

        # Assert
        assert result == mock_videos
        mock_service.get_top_videos.assert_called_once_with("401315430", 10)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_top_n_dynamics_custom_limit(self, mock_service):
        """Test top dynamics query with custom limit."""
        # Setup
        mock_dynamics = [{"dynamic_id": "123", "content": "top dynamic", "heat": 500}]
        mock_service.get_top_dynamics.return_value = mock_dynamics

        # Execute
        result = await query_module.query_top_n_dynamics("401315430", 5)

        # Assert
        assert result == mock_dynamics
        mock_service.get_top_dynamics.assert_called_once_with("401315430", 5)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_recent_relationships_default_limit(self, mock_service):
        """Test recent relationships query with default limit."""
        # Setup
        mock_relationships = [
            {"related_uid": "111", "relationship_type": "collaboration", "strength": 0.8},
            {"related_uid": "222", "relationship_type": "mention", "strength": 0.6}
        ]
        mock_service.get_recent_relationships.return_value = mock_relationships

        # Execute
        result = await query_module.query_recent_relationships("401315430")

        # Assert
        assert result == mock_relationships
        mock_service.get_recent_relationships.assert_called_once_with("401315430", 10)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_tieba_summaries_from_ai_gen_table_default_limit(self, mock_service):
        """Test tieba summaries query with default limit."""
        # Setup
        mock_summaries = [
            {"uid": "401315430", "tieba_summary": "Active in tech discussions", "datetime": "2025-01-01"},
            {"uid": "401315430", "tieba_summary": "Popular posts about gaming", "datetime": "2025-05-01"}
        ]
        mock_service.get_tieba_summaries.return_value = mock_summaries

        # Execute
        result = await query_module.query_tieba_summaries_from_ai_gen_table("401315430")

        # Assert
        assert result == mock_summaries
        mock_service.get_tieba_summaries.assert_called_once_with("401315430", 5)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_rise_reason_from_ai_gen_table_custom_limit(self, mock_service):
        """Test rise reasons query with custom limit."""
        # Setup
        mock_reasons = [
            {"uid": "401315430", "rise_reason": "Viral video content", "datetime": "2025-01-01"}
        ]
        mock_service.get_rise_reasons.return_value = mock_reasons

        # Execute
        result = await query_module.query_rise_reason_from_ai_gen_table("401315430", 3)

        # Assert
        assert result == mock_reasons
        mock_service.get_rise_reasons.assert_called_once_with("401315430", 3)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_latest_fans_medal_rank(self, mock_service):
        """Test latest fans medal rank query."""
        # Setup
        mock_rank_data = [
            {"user_uid": "111", "medal_level": 20, "intimacy": 5000},
            {"user_uid": "222", "medal_level": 18, "intimacy": 4500}
        ]
        mock_service.get_fans_medal_rank.return_value = mock_rank_data

        # Execute
        result = await query_module.query_latest_fans_medal_rank("401315430")

        # Assert
        assert result == mock_rank_data
        mock_service.get_fans_medal_rank.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_fans_medal_rank_by_datetime(self, mock_service):
        """Test fans medal rank query by specific datetime."""
        # Setup
        target_datetime = datetime(2024, 1, 1, 12, 0, 0)
        mock_rank_data = [{"user_uid": "111", "medal_level": 15, "intimacy": 3000}]
        mock_service.get_fans_medal_rank.return_value = mock_rank_data

        # Execute
        result = await query_module.query_fans_medal_rank_by_datetime("401315430", target_datetime)

        # Assert
        assert result == mock_rank_data
        mock_service.get_fans_medal_rank.assert_called_once_with("401315430", target_datetime)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_video_ai_conclusion_by_bvid_success(self, mock_service):
        """Test video AI conclusion query with successful result."""
        # Setup
        mock_conclusion = "This video demonstrates excellent content quality and engagement."
        mock_service.get_video_ai_conclusion.return_value = mock_conclusion

        # Execute
        result = await query_module.query_video_ai_conclusion_by_bvid("BV401315430")

        # Assert
        assert result == mock_conclusion
        mock_service.get_video_ai_conclusion.assert_called_once_with("BV401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_video_ai_conclusion_by_bvid_none_result(self, mock_service):
        """Test video AI conclusion query returning None."""
        # Setup
        mock_service.get_video_ai_conclusion.return_value = None

        # Execute
        result = await query_module.query_video_ai_conclusion_by_bvid("BV401315430")

        # Assert
        assert result is None
        mock_service.get_video_ai_conclusion.assert_called_once_with("BV401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_recent_comments_sentiment_value_default_days(self, mock_service):
        """Test recent comments sentiment query with default days."""
        # Setup
        mock_service.get_recent_comments_sentiment.return_value = 0.75

        # Execute
        result = await query_module.query_recent_comments_sentiment_value("401315430")

        # Assert
        assert result == 0.75
        mock_service.get_recent_comments_sentiment.assert_called_once_with("401315430", 30)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_recent_comments_sentiment_value_custom_days(self, mock_service):
        """Test recent comments sentiment query with custom days."""
        # Setup
        mock_service.get_recent_comments_sentiment.return_value = 0.65

        # Execute
        result = await query_module.query_recent_comments_sentiment_value("401315430", 7)

        # Assert
        assert result == 0.65
        mock_service.get_recent_comments_sentiment.assert_called_once_with("401315430", 7)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_query_recent_comments_sentiment_value_none_result(self, mock_service):
        """Test recent comments sentiment query returning None."""
        # Setup
        mock_service.get_recent_comments_sentiment.return_value = None

        # Execute
        result = await query_module.query_recent_comments_sentiment_value("401315430")

        # Assert
        assert result is None
        mock_service.get_recent_comments_sentiment.assert_called_once_with("401315430", 30)


class TestSpecializedFunctions:
    """Tests for functions that use specialized_service."""

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_tieba_whole(self, mock_service):
        """Test comprehensive tieba data query."""
        # Setup
        mock_tieba_data = {
            "statistics": {"total_posts": 50, "total_replies": 200},
            "threads": [
                {"thread_id": "123", "title": "Tech Discussion", "reply_count": 25},
                {"thread_id": "456", "title": "Gaming News", "reply_count": 15}
            ]
        }
        mock_service.get_tieba_data.return_value = mock_tieba_data

        # Execute
        result = await query_module.query_tieba_whole("401315430")

        # Assert
        assert result == mock_tieba_data
        mock_service.get_tieba_data.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_tieba_threads_with_threads(self, mock_service):
        """Test tieba threads query with threads data."""
        # Setup
        mock_tieba_data = {
            "threads": [
                {"thread_id": "123", "title": "Tech Discussion", "reply_count": 25},
                {"thread_id": "456", "title": "Gaming News", "reply_count": 15}
            ]
        }
        mock_service.get_tieba_data.return_value = mock_tieba_data

        # Execute
        result = await query_module.query_tieba_threads("401315430")

        # Assert
        assert result == mock_tieba_data["threads"]
        mock_service.get_tieba_data.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_tieba_threads_no_threads(self, mock_service):
        """Test tieba threads query with no threads data."""
        # Setup
        mock_tieba_data = {"statistics": {"total_posts": 0}}
        mock_service.get_tieba_data.return_value = mock_tieba_data

        # Execute
        result = await query_module.query_tieba_threads("401315430")

        # Assert
        assert result == []
        mock_service.get_tieba_data.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_latest_dahanghai_list_by_uid(self, mock_service):
        """Test latest dahanghai list query."""
        # Setup
        mock_dahanghai_list = [
            {"user_uid": "111", "user_name": "captain1", "level": 3},
            {"user_uid": "222", "user_name": "admiral2", "level": 2}
        ]
        mock_service.get_dahanghai_list.return_value = mock_dahanghai_list

        # Execute
        result = await query_module.query_latest_dahanghai_list_by_uid("401315430")

        # Assert
        assert result == mock_dahanghai_list
        mock_service.get_dahanghai_list.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_dahanghai_list_by_uid_and_datetime(self, mock_service):
        """Test dahanghai list query by specific datetime."""
        # Setup
        target_datetime = datetime(2024, 1, 1, 12, 0, 0)
        mock_dahanghai_list = [{"user_uid": "111", "user_name": "captain1", "level": 3}]
        mock_service.get_dahanghai_list.return_value = mock_dahanghai_list

        # Execute
        result = await query_module.query_dahanghai_list_by_uid_and_datetime("401315430", target_datetime)

        # Assert
        assert result == mock_dahanghai_list
        mock_service.get_dahanghai_list.assert_called_once_with("401315430", target_datetime)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_followers_list_no_datetime(self, mock_service):
        """Test followers list query without datetime."""
        # Setup
        mock_followers = [
            {"user_uid": "111", "user_name": "follower1", "follow_time": "2025-01-01"},
            {"user_uid": "222", "user_name": "follower2", "follow_time": "2025-05-01"}
        ]
        mock_service.get_followers_list.return_value = mock_followers

        # Execute
        result = await query_module.query_followers_list("401315430")

        # Assert
        assert result == mock_followers
        mock_service.get_followers_list.assert_called_once_with("401315430", None)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_followers_list_with_datetime(self, mock_service):
        """Test followers list query with specific datetime."""
        # Setup
        target_datetime = datetime(2024, 1, 1, 12, 0, 0)
        mock_followers = [{"user_uid": "111", "user_name": "follower1", "follow_time": "2025-01-01"}]
        mock_service.get_followers_list.return_value = mock_followers

        # Execute
        result = await query_module.query_followers_list("401315430", target_datetime)

        # Assert
        assert result == mock_followers
        mock_service.get_followers_list.assert_called_once_with("401315430", target_datetime)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_followers_review_list(self, mock_service):
        """Test followers review data query."""
        # Setup
        mock_review_data = {
            "review_data": [
                {"user_uid": "111", "review_status": "approved"},
                {"user_uid": "222", "review_status": "pending"}
            ],
            "review_rate": 0.85
        }
        mock_service.get_followers_review_data.return_value = mock_review_data

        # Execute
        result = await query_module.query_followers_review_list("401315430")

        # Assert
        assert result == mock_review_data
        mock_service.get_followers_review_data.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_followers_review_rate_with_rate(self, mock_service):
        """Test followers review rate query with rate data."""
        # Setup
        mock_review_data = {"review_rate": 0.75}
        mock_service.get_followers_review_data.return_value = mock_review_data

        # Execute
        result = await query_module.query_followers_review_rate("401315430")

        # Assert
        assert result == 0.75
        mock_service.get_followers_review_data.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_followers_review_rate_no_rate(self, mock_service):
        """Test followers review rate query without rate data."""
        # Setup
        mock_review_data = {"review_data": []}
        mock_service.get_followers_review_data.return_value = mock_review_data

        # Execute
        result = await query_module.query_followers_review_rate("401315430")

        # Assert
        assert result == 0.0
        mock_service.get_followers_review_data.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_comment_wordcloud_default_limit(self, mock_service):
        """Test comment wordcloud generation with default limit."""
        # Setup
        mock_wordcloud_path = "/path/to/wordcloud_401315430.png"
        mock_service.generate_comment_wordcloud.return_value = mock_wordcloud_path

        # Execute
        result = await query_module.query_comment_wordcloud("401315430")

        # Assert
        assert result == mock_wordcloud_path
        mock_service.generate_comment_wordcloud.assert_called_once_with("401315430", 1000)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_comment_wordcloud_custom_limit(self, mock_service):
        """Test comment wordcloud generation with custom limit."""
        # Setup
        mock_wordcloud_path = "/path/to/wordcloud_401315430.png"
        mock_service.generate_comment_wordcloud.return_value = mock_wordcloud_path

        # Execute
        result = await query_module.query_comment_wordcloud("401315430", 500)

        # Assert
        assert result == mock_wordcloud_path
        mock_service.generate_comment_wordcloud.assert_called_once_with("401315430", 500)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_comment_wordcloud_none_result(self, mock_service):
        """Test comment wordcloud generation returning None."""
        # Setup
        mock_service.generate_comment_wordcloud.return_value = None

        # Execute
        result = await query_module.query_comment_wordcloud("401315430")

        # Assert
        assert result is None
        mock_service.generate_comment_wordcloud.assert_called_once_with("401315430", 1000)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_cleanup_old_wordcloud_files(self, mock_service):
        """Test old wordcloud files cleanup."""
        # Setup
        mock_service.cleanup_old_wordcloud_files.return_value = 5

        # Execute
        result = await query_module.cleanup_old_wordcloud_files()

        # Assert
        assert result == 5
        mock_service.cleanup_old_wordcloud_files.assert_called_once()

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_recent_info(self, mock_service):
        """Test comprehensive user info query."""
        # Setup
        mock_comprehensive_info = {
            "user_info": {"uid": "401315430", "name": "test_user"},
            "current_stats": {"follower_num": 1000, "video_total_num": 50},
            "top_content": {"videos": [], "dynamics": []},
            "growth_metrics": {"follower_growth": "+5%", "dahanghai_growth": "+2%"}
        }
        mock_service.get_comprehensive_user_info.return_value = mock_comprehensive_info

        # Execute
        result = await query_module.query_recent_info("401315430")

        # Assert
        assert result == mock_comprehensive_info
        mock_service.get_comprehensive_user_info.assert_called_once_with("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_recent_info_with_view_no_videos(self, mock_service):
        """Test comprehensive user info with view data but no videos."""
        # Setup
        mock_comprehensive_info = {
            "user_info": {"uid": "401315430", "name": "test_user"},
            "top_content": {"videos": [], "dynamics": []}
        }
        mock_service.get_comprehensive_user_info.return_value = mock_comprehensive_info

        # Execute
        result = await query_module.query_recent_info_with_view("401315430")

        # Assert
        assert result == mock_comprehensive_info
        mock_service.get_comprehensive_user_info.assert_called_once_with("401315430")
        # Should not call get_video_day_data since no videos

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_recent_info_with_view_with_videos(self, mock_service):
        """Test comprehensive user info with view data and videos."""
        # Setup
        mock_comprehensive_info = {
            "user_info": {"uid": "401315430", "name": "test_user"},
            "top_content": {
                "videos": [{"bvid": "BV401315430", "title": "test video"}],
                "dynamics": []
            }
        }
        mock_video_day_data = [
            {"date": "2025-01-01", "views": 1000, "likes": 50},
            {"date": "2025-05-01", "views": 1200, "likes": 60}
        ]
        mock_service.get_comprehensive_user_info.return_value = mock_comprehensive_info
        mock_service.get_video_day_data.return_value = mock_video_day_data

        # Execute
        result = await query_module.query_recent_info_with_view("401315430")

        # Assert
        expected_result = mock_comprehensive_info.copy()
        expected_result["video_day_data"] = mock_video_day_data
        assert result == expected_result
        mock_service.get_comprehensive_user_info.assert_called_once_with("401315430")
        mock_service.get_video_day_data.assert_called_once_with("401315430", "BV401315430", 30)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_query_recent_info_with_view_video_no_bvid(self, mock_service):
        """Test comprehensive user info with view data but video has no bvid."""
        # Setup
        mock_comprehensive_info = {
            "user_info": {"uid": "401315430", "name": "test_user"},
            "top_content": {
                "videos": [{"title": "test video"}],  # No bvid
                "dynamics": []
            }
        }
        mock_service.get_comprehensive_user_info.return_value = mock_comprehensive_info

        # Execute
        result = await query_module.query_recent_info_with_view("401315430")

        # Assert
        assert result == mock_comprehensive_info
        mock_service.get_comprehensive_user_info.assert_called_once_with("401315430")
        # Should not call get_video_day_data since no bvid


class TestUtilityFunctions:
    """Tests for utility functions."""

    def test_get_service_instances(self):
        """Test getting service instances."""
        # Execute
        result = query_module.get_service_instances()

        # Assert
        assert isinstance(result, dict)
        assert "user_stats" in result
        assert "content" in result
        assert "analytics" in result
        assert "specialized" in result

        # Verify the services are the expected instances
        from vups_server.query.user_statistics import user_stats_service
        from vups_server.query.content_queries import content_service
        from vups_server.query.analytics import analytics_service
        from vups_server.query.specialized_queries import specialized_service

        assert result["user_stats"] is user_stats_service
        assert result["content"] is content_service
        assert result["analytics"] is analytics_service
        assert result["specialized"] is specialized_service


class TestErrorHandling:
    """Tests for error handling scenarios."""

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_service_exception_propagation(self, mock_service):
        """Test that service exceptions are properly propagated."""
        # Setup
        mock_service.get_current_stat_by_uid.side_effect = Exception("Database connection error")

        # Execute & Assert
        with pytest.raises(Exception, match="Database connection error"):
            await query_module.query_current_stat_by_mid("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.content_service')
    async def test_content_service_exception(self, mock_service):
        """Test content service exception handling."""
        # Setup
        mock_service.get_user_info_by_uid.side_effect = ValueError("Invalid UID format")

        # Execute & Assert
        with pytest.raises(ValueError, match="Invalid UID format"):
            await query_module.query_user_info_by_mid("invalid_uid")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_analytics_service_exception(self, mock_service):
        """Test analytics service exception handling."""
        # Setup
        mock_service.get_top_comments.side_effect = RuntimeError("Analytics processing failed")

        # Execute & Assert
        with pytest.raises(RuntimeError, match="Analytics processing failed"):
            await query_module.query_top_n_comments("401315430")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.specialized_service')
    async def test_specialized_service_exception(self, mock_service):
        """Test specialized service exception handling."""
        # Setup
        mock_service.get_tieba_data.side_effect = ConnectionError("External service unavailable")

        # Execute & Assert
        with pytest.raises(ConnectionError, match="External service unavailable"):
            await query_module.query_tieba_whole("401315430")


class TestParameterValidation:
    """Tests for parameter validation and edge cases."""

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_empty_uid_parameter(self, mock_service):
        """Test function behavior with empty UID."""
        # Setup
        mock_service.get_current_stat_by_uid.return_value = None

        # Execute
        result = await query_module.query_current_stat_by_mid("")

        # Assert
        assert result is None
        mock_service.get_current_stat_by_uid.assert_called_once_with("")

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_zero_limit_parameter(self, mock_service):
        """Test function behavior with zero limit."""
        # Setup
        mock_service.get_top_comments.return_value = []

        # Execute
        result = await query_module.query_top_n_comments("401315430", 0)

        # Assert
        assert result == []
        mock_service.get_top_comments.assert_called_once_with("401315430", 0)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.analytics_service')
    async def test_negative_limit_parameter(self, mock_service):
        """Test function behavior with negative limit."""
        # Setup
        mock_service.get_top_videos.return_value = []

        # Execute
        result = await query_module.query_top_n_videos("401315430", -5)

        # Assert
        assert result == []
        mock_service.get_top_videos.assert_called_once_with("401315430", -5)

    @pytest.mark.asyncio
    @patch('vups_server.query.query_vup_user_data_refactored.user_stats_service')
    async def test_negative_recent_days_parameter(self, mock_service):
        """Test function behavior with negative recent days."""
        # Setup
        mock_service.get_user_stats_recent_days.return_value = []

        # Execute
        result = await query_module.query_whole_user_all_stat_by_uid_and_recent("401315430", -10)

        # Assert
        assert result == []
        mock_service.get_user_stats_recent_days.assert_called_once_with("401315430", -10)
