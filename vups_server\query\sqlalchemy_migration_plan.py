"""
SQLAlchemy Migration Plan for VUPS Query System
This module demonstrates a phased approach to migrating from raw SQL to SQLAlchemy
while maintaining performance and handling dynamic table challenges.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
import asyncio

from sqlalchemy import (
    create_engine, MetaData, Table, Column, Integer, String, DateTime, 
    BigInteger, Text, select, and_, desc, func, text
)
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.dialects.postgresql import JSONB
import asyncpg

from vups.logger import logger


# ============================================================================
# PHASE 1: SQLAlchemy Core for Static Tables
# ============================================================================

Base = declarative_base()
metadata = MetaData()

# Define static table schemas
current_stat_table = Table(
    'current_stat_table', metadata,
    Column('uid', String, primary_key=True),
    Column('name', String),
    Column('datetime', DateTime, primary_key=True),
    Column('video_total_num', Integer),
    Column('article_total_num', Integer),
    Column('likes_total_num', BigInteger),
    Column('elec_num', Integer),
    Column('follower_num', Integer),
    Column('dahanghai_num', Integer),
)

user_info_table = Table(
    'user_info_table', metadata,
    Column('uid', String, primary_key=True),
    Column('name', String),
    Column('face', String),
    Column('sign', Text),
    Column('birthday', String),
    Column('top_photo', String),
    Column('room_id', String),
    Column('live_url', String),
)

videos_table = Table(
    'videos_table', metadata,
    Column('uid', String),
    Column('bvid', String, primary_key=True),
    Column('video_name', String),
    Column('datetime', DateTime),
    Column('play_num', BigInteger),
    Column('comment_num', Integer),
    Column('like_num', Integer),
    Column('coin_num', Integer),
    Column('favorite_num', Integer),
    Column('share_num', Integer),
    Column('heat', BigInteger),
    Column('video_ai_conclusion', Text),
)

dynamics_table = Table(
    'dynamics_table', metadata,
    Column('uid', String),
    Column('dynamic_id', String, primary_key=True),
    Column('dynamic_content', Text),
    Column('datetime', DateTime),
    Column('like_num', Integer),
    Column('comment_num', Integer),
    Column('share_num', Integer),
    Column('heat', BigInteger),
)


class HybridQueryService:
    """
    Hybrid query service that uses SQLAlchemy Core for static tables
    and raw SQL for dynamic tables and complex queries.
    """
    
    def __init__(self, database_url: str):
        self.engine = create_async_engine(database_url)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        # Keep asyncpg connection for dynamic tables
        self._asyncpg_pool = None
    
    async def init_asyncpg_pool(self, database_url: str):
        """Initialize asyncpg pool for dynamic table queries."""
        self._asyncpg_pool = await asyncpg.create_pool(database_url)
    
    # ========================================================================
    # PHASE 1: Static Tables with SQLAlchemy Core
    # ========================================================================
    
    async def get_current_stat_sqlalchemy(self, uid: str) -> Optional[Dict]:
        """
        Get current statistics using SQLAlchemy Core.
        Example of Phase 1 migration.
        """
        async with self.async_session() as session:
            query = select(current_stat_table).where(
                current_stat_table.c.uid == uid
            ).order_by(desc(current_stat_table.c.datetime)).limit(1)
            
            result = await session.execute(query)
            row = result.fetchone()
            
            if row:
                return {
                    "uid": row.uid,
                    "name": row.name,
                    "datetime": row.datetime,
                    "video_total_num": row.video_total_num,
                    "article_total_num": row.article_total_num,
                    "likes_total_num": row.likes_total_num,
                    "elec_num": row.elec_num,
                    "follower_num": row.follower_num,
                    "dahanghai_num": row.dahanghai_num,
                }
            return None
    
    async def get_user_info_sqlalchemy(self, uid: str) -> Optional[Dict]:
        """Get user info using SQLAlchemy Core."""
        async with self.async_session() as session:
            query = select(user_info_table).where(user_info_table.c.uid == uid)
            result = await session.execute(query)
            row = result.fetchone()
            
            if row:
                return {
                    "uid": row.uid,
                    "name": row.name,
                    "face": row.face,
                    "sign": row.sign,
                    "birthday": row.birthday,
                    "top_photo": row.top_photo,
                    "room_id": row.room_id,
                    "live_url": row.live_url,
                }
            return None
    
    async def get_top_videos_sqlalchemy(self, uid: str, limit: int = 10) -> List[Dict]:
        """Get top videos using SQLAlchemy Core with computed heat."""
        async with self.async_session() as session:
            # Use SQLAlchemy's func for calculations
            heat_calc = (
                videos_table.c.play_num * 1 +
                videos_table.c.like_num * 5 +
                videos_table.c.coin_num * 10 +
                videos_table.c.favorite_num * 10 +
                videos_table.c.share_num * 15 +
                videos_table.c.comment_num * 3
            ).label('calculated_heat')
            
            query = select(
                videos_table,
                heat_calc
            ).where(
                videos_table.c.uid == uid
            ).order_by(desc(heat_calc)).limit(limit)
            
            result = await session.execute(query)
            rows = result.fetchall()
            
            return [
                {
                    "uid": row.uid,
                    "bvid": row.bvid,
                    "video_name": row.video_name,
                    "datetime": row.datetime.isoformat() if row.datetime else None,
                    "play_num": row.play_num,
                    "comment_num": row.comment_num,
                    "like_num": row.like_num,
                    "coin_num": row.coin_num,
                    "favorite_num": row.favorite_num,
                    "share_num": row.share_num,
                    "heat": row.calculated_heat,
                }
                for row in rows
            ]
    
    # ========================================================================
    # PHASE 2: Hybrid Approach for Dynamic Tables
    # ========================================================================
    
    async def get_video_comments_hybrid(self, uid: str) -> List[Dict]:
        """
        Get video comments using hybrid approach:
        - SQLAlchemy text() for dynamic table names
        - Raw SQL for complex operations
        """
        async with self.async_session() as session:
            # Use SQLAlchemy's text() for dynamic table names
            comment_table_name = f"video_comment_{uid}"
            
            # Check if table exists using SQLAlchemy
            table_exists_query = text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = :table_name
                )
            """)
            
            result = await session.execute(
                table_exists_query, 
                {"table_name": comment_table_name}
            )
            table_exists = result.scalar()
            
            if not table_exists:
                return []
            
            # Query the dynamic table
            comments_query = text(f"""
                SELECT comment_id, bvid, comment_content, comment_time,
                       like_num, comment_user_uid, comment_user_name
                FROM {comment_table_name}
                ORDER BY comment_time DESC
                LIMIT 1000
            """)
            
            result = await session.execute(comments_query)
            rows = result.fetchall()
            
            return [
                {
                    "comment_id": row.comment_id,
                    "bvid": row.bvid,
                    "comment_content": row.comment_content,
                    "comment_time": row.comment_time.isoformat() if row.comment_time else None,
                    "like_num": row.like_num,
                    "comment_user_uid": row.comment_user_uid,
                    "comment_user_name": row.comment_user_name,
                }
                for row in rows
            ]
    
    # ========================================================================
    # PHASE 3: Complex Queries with Raw SQL Fallback
    # ========================================================================
    
    async def get_complex_analytics_raw(self, uid: str) -> Dict:
        """
        Complex analytics that are better suited for raw SQL.
        Keep using asyncpg for maximum performance.
        """
        if not self._asyncpg_pool:
            raise RuntimeError("AsyncPG pool not initialized")
        
        async with self._asyncpg_pool.acquire() as conn:
            # Complex query with PostgreSQL-specific features
            query = """
                WITH video_stats AS (
                    SELECT 
                        DATE_TRUNC('day', datetime) as day,
                        COUNT(*) as video_count,
                        AVG(play_num) as avg_plays,
                        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY play_num) as median_plays
                    FROM videos_table 
                    WHERE uid = $1 
                        AND datetime >= NOW() - INTERVAL '30 days'
                    GROUP BY DATE_TRUNC('day', datetime)
                ),
                follower_growth AS (
                    SELECT 
                        DATE_TRUNC('day', datetime) as day,
                        follower_num,
                        LAG(follower_num) OVER (ORDER BY datetime) as prev_followers
                    FROM current_stat_table
                    WHERE uid = $1
                        AND datetime >= NOW() - INTERVAL '30 days'
                    ORDER BY datetime
                )
                SELECT 
                    vs.day,
                    vs.video_count,
                    vs.avg_plays,
                    vs.median_plays,
                    fg.follower_num,
                    COALESCE(fg.follower_num - fg.prev_followers, 0) as daily_follower_change
                FROM video_stats vs
                FULL OUTER JOIN follower_growth fg ON vs.day = fg.day
                ORDER BY COALESCE(vs.day, fg.day) DESC
            """
            
            results = await conn.fetch(query, uid)
            
            return {
                "analytics_data": [
                    {
                        "day": row["day"].isoformat() if row["day"] else None,
                        "video_count": row["video_count"],
                        "avg_plays": float(row["avg_plays"]) if row["avg_plays"] else 0,
                        "median_plays": float(row["median_plays"]) if row["median_plays"] else 0,
                        "follower_num": row["follower_num"],
                        "daily_follower_change": row["daily_follower_change"],
                    }
                    for row in results
                ]
            }
    
    # ========================================================================
    # Performance Comparison Methods
    # ========================================================================
    
    async def performance_comparison(self, uid: str) -> Dict:
        """Compare performance between SQLAlchemy and raw SQL approaches."""
        import time
        
        results = {}
        
        # Test SQLAlchemy Core
        start_time = time.time()
        sqlalchemy_result = await self.get_current_stat_sqlalchemy(uid)
        sqlalchemy_time = time.time() - start_time
        
        # Test raw SQL (using asyncpg)
        start_time = time.time()
        async with self._asyncpg_pool.acquire() as conn:
            raw_result = await conn.fetchrow(
                "SELECT * FROM current_stat_table WHERE uid = $1 ORDER BY datetime DESC LIMIT 1",
                uid
            )
        raw_sql_time = time.time() - start_time
        
        results = {
            "sqlalchemy_time": sqlalchemy_time,
            "raw_sql_time": raw_sql_time,
            "performance_difference": ((sqlalchemy_time - raw_sql_time) / raw_sql_time) * 100,
            "sqlalchemy_result_count": 1 if sqlalchemy_result else 0,
            "raw_sql_result_count": 1 if raw_result else 0,
        }
        
        return results


# ============================================================================
# Migration Strategy Implementation
# ============================================================================

class MigrationStrategy:
    """Manages the phased migration to SQLAlchemy."""
    
    @staticmethod
    def get_migration_phases() -> Dict[str, List[str]]:
        """Define migration phases and their scope."""
        return {
            "phase_1_static_tables": [
                "current_stat_table",
                "user_info_table", 
                "videos_table",
                "dynamics_table",
                "ai_gen_table",
                "tieba_table",
            ],
            "phase_2_hybrid_dynamic": [
                "video_comment_{uid} tables",
                "dynamics_comment_{uid} tables",
                "Complex queries with text()",
            ],
            "phase_3_optimization": [
                "Performance tuning",
                "Connection pool optimization",
                "Query plan analysis",
                "Caching integration",
            ]
        }
    
    @staticmethod
    def get_recommended_approach() -> Dict[str, str]:
        """Get recommended approach for different query types."""
        return {
            "simple_selects": "SQLAlchemy Core",
            "complex_aggregations": "Raw SQL with text()",
            "dynamic_tables": "Raw SQL with asyncpg",
            "postgresql_specific": "Raw SQL with asyncpg",
            "time_series_analytics": "Raw SQL with asyncpg",
            "bulk_operations": "Raw SQL with asyncpg",
        }
